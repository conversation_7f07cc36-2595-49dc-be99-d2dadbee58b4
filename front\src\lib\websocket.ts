// WebSocket data types
interface AgentStatusData {
  agentId: number
  isOnline: boolean
  lastSeenAt: string
  activeSessions: number
}

interface SessionData {
  sessionId: string
  status: string
  platform: string
  isHandedOver: boolean
  agentId?: number
  messageCount: number
  lastMessageAt?: string
}

interface HandoverData {
  id: number
  sessionId: string
  requestedBy: string
  reason?: string
  priority: string
  status: string
  requestedAt: string
}

interface NotificationData {
  id: string
  type: 'info' | 'warning' | 'error' | 'success'
  title: string
  message: string
  urgent?: boolean
  actionUrl?: string
  timestamp: string
}

interface WebSocketMessage {
  type: string
  data:
    | AgentStatusData
    | SessionData
    | HandoverData
    | NotificationData
    | Record<string, unknown>
  timestamp?: string
  id?: string
}

interface WebSocketCallbacks {
  onMessage?: (message: WebSocketMessage) => void
  onConnect?: () => void
  onDisconnect?: () => void
  onError?: (error: Event) => void
  onAgentStatusUpdate?: (data: AgentStatusData) => void
  onSessionUpdate?: (data: SessionData) => void
  onHandoverRequest?: (data: HandoverData) => void
  onNotification?: (data: NotificationData) => void
}

// WebSocket event types
export const WS_EVENTS = {
  // Connection events
  REGISTER: 'register',
  PING: 'ping',
  PONG: 'pong',

  // Agent events
  AGENT_STATUS_UPDATE: 'agent_status_update',
  AGENT_ONLINE: 'agent_online',
  AGENT_OFFLINE: 'agent_offline',

  // Session events
  SESSION_CREATED: 'session_created',
  SESSION_UPDATED: 'session_updated',
  SESSION_ENDED: 'session_ended',
  SESSION_MESSAGE: 'session_message',

  // Handover events
  HANDOVER_REQUESTED: 'handover_requested',
  HANDOVER_ASSIGNED: 'handover_assigned',
  HANDOVER_COMPLETED: 'handover_completed',

  // Notification events
  NOTIFICATION: 'notification',
  URGENT_NOTIFICATION: 'urgent_notification',

  // Stats events
  STATS_UPDATE: 'stats_update',
} as const

class WebSocketService {
  private ws: WebSocket | null = null
  private callbacks: WebSocketCallbacks = {}
  private reconnectAttempts = 0
  private maxReconnectAttempts = 5
  private reconnectDelay = 1000
  private isConnecting = false
  private pingInterval: NodeJS.Timeout | null = null
  private connectionId: string | null = null

  connect(url: string, callbacks: WebSocketCallbacks = {}) {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      return
    }

    if (this.isConnecting) {
      return
    }

    this.isConnecting = true
    this.callbacks = callbacks

    try {
      this.ws = new WebSocket(url)

      this.ws.onopen = () => {
        console.log('WebSocket connected')
        this.isConnecting = false
        this.reconnectAttempts = 0
        this.connectionId = this.generateConnectionId()
        this.startPingInterval()
        this.callbacks.onConnect?.()
      }

      this.ws.onmessage = event => {
        try {
          const message: WebSocketMessage = JSON.parse(event.data)
          this.handleMessage(message)
        } catch (error) {
          console.error('Failed to parse WebSocket message:', error)
        }
      }

      this.ws.onclose = () => {
        console.log('WebSocket disconnected')
        this.isConnecting = false
        this.stopPingInterval()
        this.callbacks.onDisconnect?.()
        this.attemptReconnect(url)
      }

      this.ws.onerror = error => {
        console.warn('WebSocket connection failed:', error)
        this.isConnecting = false
        // Don't call the error callback for connection failures during development
        // This prevents console errors when WebSocket server is not available
        if (process.env.NODE_ENV === 'development') {
          console.log('WebSocket connection failed - this is normal if the server WebSocket is not running')
        } else {
          this.callbacks.onError?.(error)
        }
      }
    } catch (error) {
      console.error('Failed to create WebSocket connection:', error)
      this.isConnecting = false
    }
  }

  private handleMessage(message: WebSocketMessage) {
    // Call the general message handler
    this.callbacks.onMessage?.(message)

    // Call specific event handlers based on message type
    switch (message.type) {
      case WS_EVENTS.AGENT_STATUS_UPDATE:
      case WS_EVENTS.AGENT_ONLINE:
      case WS_EVENTS.AGENT_OFFLINE:
        this.callbacks.onAgentStatusUpdate?.(message.data as AgentStatusData)
        this.handleAgentStatusNotification(
          message.type,
          message.data as AgentStatusData
        )
        break

      case WS_EVENTS.SESSION_CREATED:
      case WS_EVENTS.SESSION_UPDATED:
      case WS_EVENTS.SESSION_ENDED:
      case WS_EVENTS.SESSION_MESSAGE:
        this.callbacks.onSessionUpdate?.(message.data as SessionData)
        this.handleSessionNotification(
          message.type,
          message.data as SessionData
        )
        break

      case WS_EVENTS.HANDOVER_REQUESTED:
      case WS_EVENTS.HANDOVER_ASSIGNED:
      case WS_EVENTS.HANDOVER_COMPLETED:
        this.callbacks.onHandoverRequest?.(message.data as HandoverData)
        this.handleHandoverNotification(
          message.type,
          message.data as HandoverData
        )
        break

      case WS_EVENTS.NOTIFICATION:
      case WS_EVENTS.URGENT_NOTIFICATION:
        this.callbacks.onNotification?.(message.data as NotificationData)
        this.handleDirectNotification(message.data as NotificationData)
        break

      case WS_EVENTS.PONG:
        // Handle pong response
        console.log('Received pong from server')
        break

      default:
        console.log('Unhandled WebSocket message type:', message.type)
    }
  }

  private handleAgentStatusNotification(
    eventType: string,
    data: AgentStatusData
  ) {
    // Only show notifications for other agents, not self
    if (typeof window !== 'undefined') {
      const { useNotifications } = require('@/lib/notifications')
      const { addNotification } = useNotifications.getState()

      if (eventType === WS_EVENTS.AGENT_ONLINE) {
        addNotification({
          type: 'info',
          title: 'Agent Online',
          message: 'Agent is now available to handle sessions',
        })
      } else if (eventType === WS_EVENTS.AGENT_OFFLINE) {
        addNotification({
          type: 'warning',
          title: 'Agent Offline',
          message: 'Agent has gone offline',
        })
      }
    }
  }

  private handleSessionNotification(eventType: string, data: SessionData) {
    if (typeof window !== 'undefined') {
      const { useNotifications } = require('@/lib/notifications')
      const { addNotification } = useNotifications.getState()

      switch (eventType) {
        case WS_EVENTS.SESSION_CREATED:
          addNotification({
            type: 'info',
            title: 'New Session',
            message: `New chat session started on ${data.platform}`,
            actionUrl: '/admin/sessions',
          })
          break

        case WS_EVENTS.SESSION_ENDED:
          addNotification({
            type: 'success',
            title: 'Session Completed',
            message: 'Chat session has been completed',
            actionUrl: '/admin/sessions',
          })
          break
      }
    }
  }

  private handleHandoverNotification(eventType: string, data: HandoverData) {
    if (typeof window !== 'undefined') {
      const { useNotifications } = require('@/lib/notifications')
      const { addNotification } = useNotifications.getState()

      switch (eventType) {
        case WS_EVENTS.HANDOVER_REQUESTED:
          addNotification({
            type: 'warning',
            title: 'Handover Request',
            message: `Session ${data.sessionId.slice(-8)} needs agent assistance`,
            urgent: data.priority === 'high' || data.priority === 'urgent',
            actionUrl: `/agent/chat/${data.sessionId}`,
          })
          break

        case WS_EVENTS.HANDOVER_ASSIGNED:
          addNotification({
            type: 'info',
            title: 'Handover Assigned',
            message: `You have been assigned to handle session ${data.sessionId.slice(-8)}`,
            actionUrl: `/agent/chat/${data.sessionId}`,
          })
          break

        case WS_EVENTS.HANDOVER_COMPLETED:
          addNotification({
            type: 'success',
            title: 'Handover Completed',
            message: `Session ${data.sessionId.slice(-8)} handover has been completed`,
          })
          break
      }
    }
  }

  private handleDirectNotification(data: NotificationData) {
    if (typeof window !== 'undefined') {
      const { useNotifications } = require('@/lib/notifications')
      const { addNotification } = useNotifications.getState()

      addNotification({
        type: data.type,
        title: data.title,
        message: data.message,
        urgent: data.urgent,
        actionUrl: data.actionUrl,
      })
    }
  }

  private generateConnectionId(): string {
    return `conn_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  private startPingInterval() {
    this.stopPingInterval()
    this.pingInterval = setInterval(() => {
      this.send({
        type: WS_EVENTS.PING,
        data: {
          connectionId: this.connectionId,
          timestamp: new Date().toISOString(),
        },
      })
    }, 30000) // Ping every 30 seconds
  }

  private stopPingInterval() {
    if (this.pingInterval) {
      clearInterval(this.pingInterval)
      this.pingInterval = null
    }
  }

  private attemptReconnect(url: string) {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      if (process.env.NODE_ENV === 'development') {
        console.log('WebSocket reconnection stopped - server may not be running')
      } else {
        console.log('Max reconnection attempts reached')
      }
      return
    }

    this.reconnectAttempts++
    const delay = this.reconnectDelay * 2 ** (this.reconnectAttempts - 1)

    if (process.env.NODE_ENV === 'development') {
      console.log(
        `WebSocket reconnecting in ${delay}ms (attempt ${this.reconnectAttempts})`
      )
    }

    setTimeout(() => {
      this.connect(url, this.callbacks)
    }, delay)
  }

  send(message: WebSocketMessage) {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify(message))
    } else {
      console.warn('WebSocket is not connected')
    }
  }

  disconnect() {
    this.stopPingInterval()
    if (this.ws) {
      this.ws.close()
      this.ws = null
    }
    this.connectionId = null
    this.reconnectAttempts = this.maxReconnectAttempts // Prevent reconnection
  }

  isConnected(): boolean {
    return this.ws?.readyState === WebSocket.OPEN
  }

  // Utility methods for common WebSocket operations
  registerAgent(agentId: number, sessionId?: string) {
    this.send({
      type: WS_EVENTS.REGISTER,
      data: {
        connectionType: 'agent',
        agentId,
        sessionId,
        connectionId: this.connectionId,
      },
    })
  }

  registerUser(sessionId: string) {
    this.send({
      type: WS_EVENTS.REGISTER,
      data: {
        connectionType: 'user',
        sessionId,
        connectionId: this.connectionId,
      },
    })
  }

  updateAgentStatus(isOnline: boolean) {
    this.send({
      type: WS_EVENTS.AGENT_STATUS_UPDATE,
      data: {
        isOnline,
        timestamp: new Date().toISOString(),
      },
    })
  }

  requestHandover(sessionId: string, reason?: string, priority = 'normal') {
    this.send({
      type: WS_EVENTS.HANDOVER_REQUESTED,
      data: {
        sessionId,
        reason,
        priority,
        timestamp: new Date().toISOString(),
      },
    })
  }
}

// Create a singleton instance
export const websocketService = new WebSocketService()

// Hook for React components
import React from 'react'

export function useWebSocket(url: string, callbacks: WebSocketCallbacks = {}) {
  const [isConnected, setIsConnected] = React.useState(false)

  // Memoize callbacks to prevent infinite reconnections
  const memoizedCallbacks = React.useMemo(
    () => callbacks,
    [
      callbacks.onConnect,
      callbacks.onDisconnect,
      callbacks.onMessage,
      callbacks.onError,
      callbacks.onAgentStatusUpdate,
      callbacks.onNotification,
      callbacks.onHandoverRequested,
      callbacks.onHandoverAccepted,
      callbacks.onHandoverCompleted,
      callbacks.onSessionUpdate,
    ]
  )

  React.useEffect(() => {
    const enhancedCallbacks = {
      ...memoizedCallbacks,
      onConnect: () => {
        setIsConnected(true)
        memoizedCallbacks.onConnect?.()
      },
      onDisconnect: () => {
        setIsConnected(false)
        memoizedCallbacks.onDisconnect?.()
      },
    }

    websocketService.connect(url, enhancedCallbacks)

    return () => {
      websocketService.disconnect()
    }
  }, [url, memoizedCallbacks])

  const sendMessage = React.useCallback((message: WebSocketMessage) => {
    websocketService.send(message)
  }, [])

  return {
    isConnected,
    sendMessage,
    disconnect: () => websocketService.disconnect(),
  }
}

// For non-React usage
export default websocketService
